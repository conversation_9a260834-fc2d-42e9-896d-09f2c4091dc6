#!/usr/bin/env python3
import re

def clean_logs(content):
    # Remove log.info statements with LOG_LEVEL checks
    content = re.sub(r'\s*if\s+LOG_LEVEL\s*<=\s*log\.LOGLEVEL_INFO\s+then\s*\n\s*log\.info\([^)]*\)\s*\n\s*end', '', content, flags=re.MULTILINE)
    
    # Remove log.warn statements with LOG_LEVEL checks  
    content = re.sub(r'\s*if\s+LOG_LEVEL\s*<=\s*log\.LOGLEVEL_WARN\s+then\s*\n\s*log\.warn\([^)]*\)\s*\n\s*end', '', content, flags=re.MULTILINE)
    
    # Remove standalone log.info statements (but keep error logs)
    content = re.sub(r'\n\s*log\.info\([^)]*\)', '', content)
    
    # Remove standalone log.warn statements
    content = re.sub(r'\n\s*log\.warn\([^)]*\)', '', content)
    
    # Keep log.error statements as they are critical for debugging issues
    
    return content

# Read the file
with open('main.lua', 'r', encoding='utf-8') as f:
    content = f.read()

# Clean the logs
cleaned_content = clean_logs(content)

# Write the cleaned content back
with open('main.lua', 'w', encoding='utf-8') as f:
    f.write(cleaned_content)

print("Log cleanup completed successfully!")
print("Removed most log.info and log.warn statements while keeping log.error statements.")

-- Script to clean up log statements from main.lua for better performance
-- This script will be used to identify patterns and help with cleanup

local function readFile(filename)
    local file = io.open(filename, "r")
    if not file then
        print("Could not open file: " .. filename)
        return nil
    end
    local content = file:read("*all")
    file:close()
    return content
end

local function writeFile(filename, content)
    local file = io.open(filename, "w")
    if not file then
        print("Could not write to file: " .. filename)
        return false
    end
    file:write(content)
    file:close()
    return true
end

local function cleanupLogs(content)
    -- Remove log.info statements that are wrapped in LOG_LEVEL checks
    content = content:gsub('if LOG_LEVEL <= log%.LOGLEVEL_INFO then\n%s*log%.info%([^)]*%)\n%s*end', '')
    
    -- Remove log.warn statements that are wrapped in LOG_LEVEL checks  
    content = content:gsub('if LOG_LEVEL <= log%.LOGLEVEL_WARN then\n%s*log%.warn%([^)]*%)\n%s*end', '')
    
    -- Remove standalone log.info statements (but keep error logs)
    content = content:gsub('\n%s*log%.info%([^)]*%)', '')
    
    -- Remove standalone log.warn statements
    content = content:gsub('\n%s*log%.warn%([^)]*%)', '')
    
    -- Keep log.error statements as they are critical for debugging issues
    
    return content
end

-- Main execution
local content = readFile("main.lua")
if content then
    local cleaned_content = cleanupLogs(content)
    if writeFile("main_cleaned.lua", cleaned_content) then
        print("Successfully created cleaned version: main_cleaned.lua")
        print("Review the cleaned file and replace main.lua if satisfied")
    else
        print("Failed to write cleaned file")
    end
else
    print("Failed to read main.lua")
end
